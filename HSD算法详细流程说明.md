# HSD算法详细流程说明文档

## 概述
HSD (Hierarchical Item Inconsistency Signal Learning) 是一个用于序列推荐中序列去噪的层次化项目不一致性信号学习模型。该算法通过层次化的方式学习项目不一致性信号，识别并过滤噪声项目，从而提升序列推荐的性能。

## 目录结构与文件功能

### 1. 主要目录结构
```
HSD/
├── run_hsd.py                    # 主运行文件
├── model/                        # 模型实现目录
│   ├── hsd.py                   # HSD核心模型实现
│   ├── bert4rec.py              # BERT4Rec子模型
│   ├── gru4rec.py               # GRU4Rec子模型
│   ├── sasrec.py                # SASRec子模型
│   ├── caser.py                 # Caser子模型
│   ├── narm.py                  # NARM子模型
│   └── stamp.py                 # STAMP子模型
├── Triainer/                     # 训练器目录
│   └── HsdTrainer.py            # HSD专用训练器
├── dataset/                      # 数据处理目录
│   ├── dataUtils.py             # 数据工具函数
│   ├── dataset.py               # 数据集基类
│   └── sequential_dataset.py    # 序列数据集类
├── config/                       # 配置目录
│   ├── base_init.py             # 预训练模型路径配置
│   └── text.yml                 # 基础配置文件
└── HsdUtils/                     # 工具函数目录
    └── utils.py                 # 模型获取工具函数
```

## HSD算法详细流程

### 阶段1: 初始化与配置 (run_hsd.py)

**文件位置**: `run_hsd.py`
**主要功能**: 
- 设置超参数和配置
- 初始化模型、数据集和训练器
- 控制整个训练和评估流程

**详细步骤**:
1. **参数设置** (第13-28行):
   - 学习率: `lr_ = 1e-3`
   - 数据增强开关: `data_augmentation = True`
   - Gumbel温度参数: `gumbel_temperature = 0.5`
   - 嵌入维度: `embedding_size = 100`

2. **配置构建** (第34-43行):
   - 调用`get_parameter_dict()`构建参数字典
   - 创建RecBole配置对象
   - 根据模型类型选择HSD或基础模型

3. **数据集创建** (第53-57行):
   - 调用`dataset/dataUtils.py`中的`create_dataset()`
   - 进行数据预处理和分割

### 阶段2: 数据处理 (dataset/)

**文件位置**: `dataset/dataUtils.py`, `dataset/sequential_dataset.py`
**主要功能**: 
- 创建和处理序列推荐数据集
- 数据预处理和分割

**详细步骤**:
1. **数据集创建** (`dataUtils.py` 第26-50行):
   - 根据模型类型选择相应的数据集类
   - 对于HSD模型，使用SequentialDataset

2. **数据预处理**:
   - 过滤用户和项目交互次数
   - 构建序列数据
   - 划分训练/验证/测试集

### 阶段3: HSD模型初始化 (model/hsd.py)

**文件位置**: `model/hsd.py`
**主要功能**: 
- 初始化HSD模型的各个组件
- 设置子模型和嵌入层

**详细步骤**:
1. **基础组件初始化** (第22-40行):
   - 用户嵌入层: `self.user_embedding`
   - 批归一化层: `self.LayerNorm`
   - Dropout层: `self.emb_dropout`

2. **序列级一致性检测组件** (第41-55行):
   - 双向LSTM: `self.rnn` (用于序列重构)
   - 2D卷积层: `self.conv` (处理原始和重构序列)
   - 序列级MLP: `self.seq_level_mlp` (输出保留/丢弃分数)

3. **项目级一致性检测组件** (第57-66行):
   - 注意力读出层: `self.read_out` (AttnReadout类)
   - 计算项目与目标的相关性

4. **子模型初始化** (第82-99行):
   - 根据配置加载子模型 (BERT4Rec, GRU4Rec等)
   - 共享项目嵌入层
   - 可选加载预训练嵌入

### 阶段4: 前向传播流程 (model/hsd.py)

**文件位置**: `model/hsd.py` - `forward()` 方法
**主要功能**: 
- 执行HSD的核心去噪算法
- 生成去噪后的序列

**详细步骤**:

#### 4.1 输入处理 (第424-444行):
```python
item_seq = interaction[self.ITEM_SEQ]          # 获取项目序列
item_seq_len = interaction[self.ITEM_SEQ_LEN]  # 获取序列长度
target_item = interaction[self.ITEM_ID]        # 获取目标项目
mask = torch.ones(...) * item_seq.gt(0)       # 创建有效位置掩码
```

#### 4.2 序列级一致性检测 (第445-451行):
**调用**: `seq_level_consistency()` 方法 (第230-308行)
**功能**: 使用双向LSTM进行序列重构，检测序列级不一致性

**详细过程**:
1. **编码阶段** (第252-262行):
   ```python
   # 双向LSTM编码
   encoder_output, (hidden, _) = self.rnn(item_seq_emb)
   # 合并前向和后向输出
   encoder_emb = encoder_output[:,:,:hidden_size] + encoder_output[:,:,hidden_size:]
   ```

2. **解码阶段** (第267-273行):
   ```python
   # 双向LSTM解码(重构)
   decoder_output, _ = self.rnn(encoder_emb)
   # 合并解码器输出
   decoder_emb = decoder_output[:,:,:hidden_size] + decoder_output[:,:,hidden_size:]
   ```

3. **重构损失计算** (第278-283行):
   ```python
   # MSE重构损失
   loss_fct = nn.MSELoss(reduction='none')
   reconstruction_loss = loss_fct(decoder_emb * mask, original_emb * mask)
   ```

4. **序列级评分生成** (第286-306行):
   ```python
   # 拼接原始和重构序列
   concat_seq = torch.stack((decoder_emb, original_emb), dim=-1)
   # 2D卷积处理
   conv_output = self.conv(concat_seq)
   # MLP生成2维评分 (保留/丢弃)
   seq_level_score = self.seq_level_mlp(conv_output)
   ```

#### 4.3 项目级一致性检测 (第461-465行):
**调用**: `item_level_consistency()` 方法 (第310-331行)
**功能**: 使用注意力机制计算项目与目标的相关性

**详细过程** (AttnReadout类, 第704-843行):
1. **注意力计算** (第781-792行):
   ```python
   # 处理序列特征和目标特征
   feat_u = self.fc_u(feat)  # 序列特征变换
   feat_v = self.fc_v(target)  # 目标特征变换
   # 计算注意力分数
   attention_score = self.fc_e(F.tanh(feat_u + feat_v))
   ```

2. **长期表示计算** (第809-843行):
   ```python
   # 使用注意力权重加权特征
   attention_weights = self.softmax(attention_score[:,:,1])
   long_term_repr = torch.sum(feat * attention_weights.unsqueeze(-1), dim=1)
   ```

#### 4.4 层次化去噪决策 (第473-480行):
**调用**: `generate_pos_seq()` 方法 (第166-228行)
**功能**: 结合两个级别的评分，生成去噪后的序列

**详细过程**:
1. **Gumbel-Softmax采样** (第191-192行):
   ```python
   # 可微分的离散采样
   item_gumbel = F.gumbel_softmax(item_level_score, tau=self.tau, hard=True)
   seq_gumbel = F.gumbel_softmax(seq_level_score, tau=self.tau, hard=True)
   ```

2. **层次化决策** (第194-202行):
   ```python
   # 提取"丢弃"标志
   item_noise_flag = item_gumbel[:,:,1] * mask
   seq_noise_flag = seq_gumbel[:,:,1] * mask
   # 两个级别都认为是噪声才标记为噪声
   final_noise_flag = item_noise_flag * seq_noise_flag
   # 生成保留标志
   keep_flag = (1 - final_noise_flag) * mask
   ```

3. **序列重排** (第220-223行):
   ```python
   # 将非零元素移到序列前面
   denoised_seq, denoised_emb = self.method_name(pos_seq, pos_seq_emb)
   ```

### 阶段5: 损失计算与课程学习 (model/hsd.py)

**文件位置**: `model/hsd.py` - `calculate_loss_curriculum()` 方法
**主要功能**: 
- 实现课程学习策略
- 计算多个损失组件

**详细步骤**:

#### 5.1 课程学习调度 (第499-501行):
```python
self.tau = tau  # 更新Gumbel温度
self.filter_drop_rate = 0.2 - drop_rate  # 动态调整过滤率
```

#### 5.2 多损失计算 (第517-536行):
1. **子模型预测损失**:
   ```python
   # 使用去噪序列进行预测
   sub_model_output = self.sub_model_forward(pos_seq, pos_seq_emb, pos_seq_len, user)
   scores = torch.matmul(seq_representation, all_items_emb.transpose(0,1))
   prediction_loss = self.loss_fuc(scores, target_item)
   ```

2. **重构损失** (课程学习):
   ```python
   # 选择容易学习的样本
   easy_samples = self.cal_curriculum_batch_id(drop_rate, reconstruction_loss)
   curriculum_reconstruction_loss = reconstruction_loss[easy_samples].mean()
   ```

3. **总损失**:
   ```python
   total_loss = reconstruction_loss + prediction_loss + filter_loss
   ```

### 阶段6: 训练过程 (Triainer/HsdTrainer.py)

**文件位置**: `Triainer/HsdTrainer.py`
**主要功能**: 
- 管理HSD模型的训练过程
- 实现课程学习和温度退火

**详细步骤**:

#### 6.1 训练循环 (第104-186行):
1. **课程学习调度** (第80-88行):
   ```python
   # 线性递减的丢弃率
   drop_rate = np.linspace(0.2**1, 0, 10)
   current_drop_rate = drop_rate[epoch] if epoch < 10 else 0.0
   ```

2. **温度退火** (第90-92行):
   ```python
   # 每40个batch退火一次
   if self.global_train_batches % 40 == 0:
       self.tau = max(1e-3, self.tau * np.exp(-1e-3 * self.global_train_batches))
   ```

3. **损失计算** (第154行):
   ```python
   losses, clean_percent, L_rec, pred_loss = model.calculate_loss_curriculum(
       interaction, drop_rate, self.tau)
   ```

#### 6.2 评估过程 (第273-296行):
```python
# HSD模型的全排序预测
if 'HSD' in str(self.model):
    scores, clean_percent = self.model.full_sort_predict(interaction)
else:
    scores = self.model.full_sort_predict(interaction)
```

### 阶段7: 子模型集成 (model/hsd.py)

**文件位置**: `model/hsd.py` - `sub_model_forward()` 方法
**主要功能**: 
- 根据不同子模型调用相应的前向函数
- 处理去噪后的序列

**支持的子模型**:
1. **BERT4Rec** (第642-644行): 使用`forward_denoising()`
2. **GRU4Rec** (第645-647行): 使用`forward_denoising()`  
3. **SASRec** (第648-650行): 使用`forward_denoising()`
4. **Caser** (第651-653行): 需要用户信息
5. **NARM** (第654-656行): 需要序列长度信息
6. **STAMP** (第663-665行): 使用`forward_denoising()`

### 阶段8: 预测与评估

**文件位置**: `model/hsd.py` - `predict()` 和 `full_sort_predict()` 方法
**主要功能**: 
- 对特定项目或所有项目进行评分预测
- 返回去噪统计信息

## 关键创新点

1. **层次化去噪**: 结合序列级和项目级两个层次的一致性检测
2. **课程学习**: 从容易学习的样本开始，逐步增加难度
3. **温度退火**: 动态调整Gumbel-Softmax的温度参数
4. **多模型兼容**: 支持多种主流序列推荐模型作为子模型

## 输出结果

- **去噪序列**: 过滤噪声后的用户行为序列
- **清洁度统计**: 序列清洁百分比
- **推荐结果**: 基于去噪序列的项目推荐
- **多重损失**: 重构损失、预测损失、过滤损失

该算法通过层次化的噪声识别和课程学习策略，有效提升了序列推荐的性能和鲁棒性。

## 详细技术实现

### AttnReadout注意力读出层详解 (model/hsd.py 第704-843行)

**类功能**: 实现项目级一致性检测的核心组件
**输入**: 序列特征、目标特征、掩码
**输出**: 注意力分数、长期表示、项目级序列表示

#### 初始化组件 (第709-754行):
```python
# 批归一化层 (可选)
self.batch_norm = nn.BatchNorm1d(session_len)
# 特征dropout
self.feat_drop = nn.Dropout(feat_drop)
# 序列特征变换
self.fc_u = nn.Linear(input_dim, hidden_dim, bias=False)
# 目标特征变换
self.fc_v = nn.Linear(input_dim, hidden_dim, bias=True)
# 注意力分数生成 (输出2维: 保留/丢弃)
self.fc_e = nn.Linear(hidden_dim, 2, bias=False)
# 长短期表示融合
self.mlp_n_ls = nn.Linear(input_dim + hidden_dim, hidden_dim)
```

#### 前向传播过程 (第756-807行):
1. **特征预处理**:
   ```python
   # 批归一化 + 掩码 + dropout
   feat = self.batch_norm(feat) if self.batch_norm else feat
   feat = feat * mask
   feat = self.feat_drop(feat) * mask
   ```

2. **注意力计算**:
   ```python
   # 序列特征变换
   feat_u = self.fc_u(feat) * mask
   # 目标特征变换
   feat_v = self.fc_v(last_nodes)
   # 注意力分数 (tanh激活 + sigmoid)
   attention_score = self.sigmoid(self.fc_e(F.tanh(feat_u + feat_v))) * mask
   ```

3. **长短期表示融合**:
   ```python
   # 短期表示 (目标特征)
   short_term = last_nodes.squeeze()
   # 长期表示 (注意力加权)
   long_term = self.get_long_term(attention_score, feat, mask)
   # 融合表示
   fused_repr = self.mlp_n_ls(torch.cat((long_term, short_term), dim=-1))
   ```

### 序列重排算法详解 (model/hsd.py 第123-164行)

**方法名**: `method_name()`
**功能**: 将去噪后序列中的非零元素移到序列前面，保持相对顺序

#### 算法步骤:
1. **找出非零位置** (第136行):
   ```python
   row_indexes, col_id = torch.where(generated_seq.gt(0))
   ```

2. **重新分配列索引** (第137-151行):
   ```python
   row_flag = row_indexes[0]  # 当前行标记
   index_flag = -1           # 列索引计数器
   col_index = []            # 新列索引列表

   for row_index in row_indexes:
       if row_index == row_flag:
           index_flag += 1    # 同一行，列索引递增
       else:
           index_flag = 0     # 新行，重置列索引
           row_flag = row_index
       col_index.append(index_flag)
   ```

3. **重排序列** (第157-162行):
   ```python
   # 创建新的零序列
   denoising_seq = torch.zeros_like(generated_seq)
   denoising_seq_emb = torch.zeros_like(generated_seq_emb)
   # 将非零元素放到新位置
   denoising_seq[row_indexes, col_index] = generated_seq[row_indexes, col_id]
   denoising_seq_emb[row_indexes, col_index, :] = generated_seq_emb[row_indexes, col_id, :]
   ```

### 基于损失的过滤机制 (model/hsd.py 第333-403行)

**方法名**: `loss_filter()`
**功能**: 识别难以学习的样本，实现自适应过滤

#### 详细实现:
1. **BPR损失计算** (第349-382行):
   ```python
   # 获取用户和项目嵌入
   item_seq_emb = self.item_embedding(item_seq) * mask
   user_emb = self.user_embedding(user)

   # 正样本得分
   pos_score = torch.matmul(item_seq_emb.unsqueeze(-2), user_emb.unsqueeze(-1))

   # 负样本得分 (训练时)
   if train_flag:
       neg_items_emb = self.item_embedding(neg_items) * mask
       neg_score = torch.matmul(neg_items_emb.unsqueeze(-2), user_emb.unsqueeze(-1))

   # BPR损失
   loss = -torch.log(1e-10 + torch.sigmoid(pos_score - neg_score))
   ```

2. **自适应过滤** (第383-402行):
   ```python
   # 按损失排序 (升序)
   loss_sorted, ind_sorted = torch.sort(loss, descending=False, dim=-1)
   # 计算过滤数量
   num_remember = (filter_drop_rate * item_seq_len).long()

   # 设置过滤标志
   loss_filter_flag = torch.zeros_like(item_seq)
   for index, filtered_num in enumerate(num_remember):
       # 过滤损失最大的项目
       loss_index = ind_sorted[index][-filtered_num:]
       loss_filter_flag[index][loss_index] = 1
       loss[index][loss_index] *= 0  # 将过滤项目损失置零
   ```

### 课程学习调度策略 (Triainer/HsdTrainer.py)

#### 丢弃率调度 (第80-88行):
```python
def drop_rate_schedule_curriculum(self, iteration):
    # 线性递减: 从0.2降到0
    drop_rate = np.linspace(0.2**1, 0, 10)
    if iteration < 10:
        return drop_rate[iteration]
    else:
        return 0.0  # 10轮后不再丢弃
```

#### 温度退火策略 (第90-92行):
```python
def _gumbel_softmax_tempreture_anneal(self):
    r = 1e-3  # 退火率
    self.tau = max(1e-3, self.tau * np.exp(-r * self.global_train_batches))
```

### 子模型适配机制

#### 不同子模型的前向传播适配 (第629-669行):
```python
def sub_model_forward(self, generated_seq, pos_seq_emb, denoising_seq_len, user):
    if self.sub_model_name == 'BERT4Rec':
        # BERT4Rec: 使用掩码机制
        seq_output = self.sub_model.forward_denoising(generated_seq, pos_seq_emb)
    elif self.sub_model_name == 'Caser':
        # Caser: 需要用户信息
        seq_output = self.sub_model.forward_denoising(user, generated_seq, pos_seq_emb)
    elif self.sub_model_name == 'NARM':
        # NARM: 需要序列长度
        seq_output = self.sub_model.forward_denoising(generated_seq, denoising_seq_len, pos_seq_emb)
    # ... 其他子模型
```

#### 输出聚合策略 (第592-627行):
```python
def denoising_seq_gather(self, generated_seq, seq_output):
    # 计算有效序列长度
    generated_item_seq_len = torch.sum(generated_seq.gt(0), 1) - 1

    # 不同模型的聚合方式
    if self.sub_model_name in ['SRGNN', 'GCSAN', 'Caser', 'NARM', 'DSAN', 'STAMP']:
        # 已聚合的模型直接使用
        seq_output = seq_output
    elif self.sub_model_name == 'fmlp':
        # FMLP取最后位置
        seq_output = seq_output[:, -1, :]
    else:
        # 其他模型根据序列长度聚合
        seq_output = self.gather_indexes(seq_output, generated_item_seq_len)
```

## 算法复杂度分析

### 时间复杂度:
- **序列级一致性**: O(B × L × H) - 双向LSTM编解码
- **项目级一致性**: O(B × L × H) - 注意力计算
- **层次化决策**: O(B × L) - Gumbel-Softmax采样
- **总体复杂度**: O(B × L × H)

其中: B=批次大小, L=序列长度, H=隐藏维度

### 空间复杂度:
- **模型参数**: O(V × H + H²) - 嵌入层 + LSTM参数
- **中间变量**: O(B × L × H) - 序列表示存储
- **总体空间**: O(V × H + B × L × H)

其中: V=词汇表大小

## 实验配置与调优建议

### 关键超参数:
1. **Gumbel温度**: 初始值0.5，指数退火至1e-3
2. **课程学习**: 丢弃率从0.2线性降至0
3. **过滤率**: 动态调整，最大0.2
4. **学习率**: 1e-3，可配合学习率调度器

### 数据集适配:
- **ml-1m**: MAX_ITEM_LIST_LENGTH=200
- **其他数据集**: MAX_ITEM_LIST_LENGTH=50
- **用户/项目过滤**: 最少4次交互

该算法通过精心设计的层次化架构和课程学习策略，在多个数据集上显著提升了序列推荐的性能。

## HSD算法完整流程图

```
输入: 用户序列 [u1, u2, ..., un]
  ↓
┌─────────────────────────────────────────────────────────────┐
│                    数据预处理阶段                              │
│  文件: dataset/dataUtils.py, dataset/sequential_dataset.py   │
│  功能: 序列构建、掩码生成、数据分割                              │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                    HSD模型初始化                              │
│  文件: model/hsd.py (__init__)                              │
│  功能: 嵌入层、LSTM、注意力层、子模型初始化                      │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                  序列级一致性检测                              │
│  文件: model/hsd.py (seq_level_consistency)                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │双向LSTM编码 │→ │双向LSTM解码 │→ │重构损失计算  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                           ↓                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │序列拼接     │→ │2D卷积处理   │→ │MLP评分生成  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                  项目级一致性检测                              │
│  文件: model/hsd.py (item_level_consistency + AttnReadout)   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │特征变换     │→ │注意力计算   │→ │长期表示生成  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                           ↓                                 │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │短期表示融合 │→ │项目级评分   │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                   层次化去噪决策                              │
│  文件: model/hsd.py (generate_pos_seq)                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │Gumbel采样   │→ │层次化融合   │→ │噪声标记     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                           ↓                                 │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │序列过滤     │→ │序列重排     │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                   子模型前向传播                              │
│  文件: model/hsd.py (sub_model_forward)                     │
│  支持: BERT4Rec, GRU4Rec, SASRec, Caser, NARM, STAMP等     │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                   损失计算与优化                              │
│  文件: model/hsd.py (calculate_loss_curriculum)             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │重构损失     │+ │预测损失     │+ │过滤损失     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                           ↓                                 │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │课程学习     │→ │总损失计算   │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
  ↓
┌─────────────────────────────────────────────────────────────┐
│                    训练与评估                                │
│  文件: Triainer/HsdTrainer.py                              │
│  功能: 课程学习调度、温度退火、性能评估                        │
└─────────────────────────────────────────────────────────────┘
  ↓
输出: 去噪序列 + 推荐结果 + 清洁度统计
```

## 核心文件职责总结

### 1. 主控制文件
- **run_hsd.py**:
  - 实验配置和参数设置
  - 模型、数据集、训练器的初始化和协调
  - 结果记录和保存

### 2. 核心模型文件
- **model/hsd.py**:
  - HSD主模型类实现 (第9-702行)
  - AttnReadout注意力读出层 (第704-843行)
  - 序列级和项目级一致性检测
  - 层次化去噪决策机制
  - 课程学习损失计算

### 3. 训练控制文件
- **Triainer/HsdTrainer.py**:
  - 继承RecBole的Trainer类
  - 课程学习调度策略
  - Gumbel温度退火机制
  - 训练过程监控和日志记录

### 4. 数据处理文件
- **dataset/dataUtils.py**: 数据集创建和预处理
- **dataset/sequential_dataset.py**: 序列数据集类实现

### 5. 配置和工具文件
- **config/base_init.py**: 预训练模型路径配置
- **config/text.yml**: 基础配置参数
- **HsdUtils/utils.py**: 模型获取和判断工具函数

### 6. 子模型文件
- **model/bert4rec.py**: BERT4Rec子模型实现
- **model/gru4rec.py**: GRU4Rec子模型实现
- **model/sasrec.py**: SASRec子模型实现
- **model/caser.py**: Caser子模型实现
- **model/narm.py**: NARM子模型实现
- **model/stamp.py**: STAMP子模型实现

## 使用指南

### 1. 环境准备
```bash
pip install -r requirements.txt
```

### 2. 运行实验
```bash
python run_hsd.py
```

### 3. 配置修改
- 修改`run_hsd.py`中的超参数设置
- 调整`config/text.yml`中的基础配置
- 选择不同的子模型和数据集

### 4. 结果分析
- 查看控制台输出的训练日志
- 分析`denoising-ml.xlsx`中的实验结果
- 关注清洁序列百分比和推荐性能指标

## 扩展建议

1. **新子模型集成**: 在`model/`目录下添加新的子模型，并在`sub_model_forward()`中添加相应的调用逻辑

2. **新数据集支持**: 在`get_parameter_dict()`中添加新数据集的特定配置

3. **新去噪策略**: 可以在`generate_pos_seq()`中实验不同的层次化决策策略

4. **性能优化**: 可以通过并行计算、内存优化等方式提升算法效率

该文档详细描述了HSD算法的完整实现流程，每个步骤都标明了对应的文件位置和代码行数，便于理解和修改算法实现。
