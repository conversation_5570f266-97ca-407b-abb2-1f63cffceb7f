# @Time   : 2020/9/16
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : chen<PERSON><PERSON><PERSON>@ruc.edu.cn

# UPDATE:
# @Time   : 2020/9/16, 2021/7/1, 2021/7/11
# <AUTHOR> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# @Email  : ch<PERSON><PERSON><PERSON><PERSON>@ruc.edu.cn, <EMAIL>, <EMAIL>

"""
recbole.data.sequential_dataset
###############################
"""
import os
from recbole.utils.url import makedirs
import numpy as np
import torch

from recbole.data.interaction import Interaction
from recbole.utils.enum_type import FeatureType, FeatureSource
from dataset.dataset import Dataset


class SequentialDataset(Dataset):
    """:class:`SequentialDataset` is based on :class:`~recbole.data.dataset.dataset.Dataset`,
    and provides augmentation interface to adapt to Sequential Recommendation,
    which can accelerate the data loader.

    Attributes:
        max_item_list_len (int): Max length of historical item list.
        item_list_length_field (str): Field name for item lists' length.
    """

    def __init__(self, config):
        self.max_item_list_len = config['MAX_ITEM_LIST_LENGTH']
        self.item_list_length_field = config['ITEM_LIST_LENGTH_FIELD']
        super().__init__(config)
        if config['benchmark_filename'] is not None:
            self._benchmark_presets()

    def _change_feat_format(self):
        """Change feat format from :class:`pandas.DataFrame` to :class:`Interaction`,
           then perform data augmentation.
        """
        super()._change_feat_format()

        if self.config['benchmark_filename'] is not None:
            return
        self.logger.debug('Augmentation for sequential recommendation.')
        self.data_augmentation()

    def _aug_presets(self):
        list_suffix = self.config['LIST_SUFFIX']
        for field in self.inter_feat:
            if field != self.uid_field:
                list_field = field + list_suffix
                setattr(self, f'{field}_list_field', list_field)
                ftype = self.field2type[field]

                if ftype in [FeatureType.TOKEN, FeatureType.TOKEN_SEQ]:
                    list_ftype = FeatureType.TOKEN_SEQ
                else:
                    list_ftype = FeatureType.FLOAT_SEQ

                if ftype in [FeatureType.TOKEN_SEQ, FeatureType.FLOAT_SEQ]:
                    list_len = (self.max_item_list_len, self.field2seqlen[field])
                else:
                    list_len = self.max_item_list_len

                self.set_field_property(list_field, list_ftype, FeatureSource.INTERACTION, list_len)

        self.set_field_property(self.item_list_length_field, FeatureType.TOKEN, FeatureSource.INTERACTION, 1)

    def data_augmentation(self):
        """Augmentation processing for sequential dataset.

        E.g., ``u1`` has purchase sequence ``<i1, i2, i3, i4>``,
        then after augmentation, we will generate three cases.

        ``u1, <i1> | i2``

        (Which means given user_id ``u1`` and item_seq ``<i1>``,
        we need to predict the next item ``i2``.)

        The other cases are below:

        ``u1, <i1, i2> | i3``

        ``u1, <i1, i2, i3> | i4``
        """
        self.logger.debug('data_augmentation')

        self._aug_presets()

        self._check_field('uid_field', 'time_field')
        max_item_list_len = self.config['MAX_ITEM_LIST_LENGTH']
        self.sort(by=[self.uid_field, self.time_field], ascending=True)

        if self.config['save_for_clea'] is not None and self.config['save_for_clea']:
            dataset_dict = {}
            uid = self.inter_feat[self.uid_field]
            iid = self.inter_feat[self.iid_field].numpy()
            for i, uid in enumerate(uid.numpy()):
                if uid not in dataset_dict.keys():
                    dataset_dict[uid] = [[iid[i]]]
                else:
                    dataset_dict[uid].append([iid[i]])

            path = self.dataset_path
            if not os.path.exists(path):
                makedirs(path)
            path += '/' + self.config['dataset'] + '.txt'
            f = open(self.dataset_path + '/' + self.config['dataset'] + '.txt', 'w')
            f.write(str(dataset_dict))
            f.close()
            raise ValueError('save ' + self.config['dataset'] + ' finished')

        last_uid = None
        uid_list, item_list_index, target_index, item_list_length = [], [], [], []
        seq_start = 0
        for i, uid in enumerate(self.inter_feat[self.uid_field].numpy()):
            if last_uid != uid:
                last_uid = uid
                seq_start = i
            else:
                if i - seq_start > max_item_list_len:
                    seq_start += 1
                uid_list.append(uid)
                item_list_index.append(slice(seq_start, i))
                target_index.append(i)
                item_list_length.append(i - seq_start)

        uid_list = np.array(uid_list)
        item_list_index = np.array(item_list_index)
        target_index = np.array(target_index)
        item_list_length = np.array(item_list_length, dtype=np.int64)

        new_length = len(item_list_index)
        new_data = self.inter_feat[target_index]
        new_dict = {
            self.item_list_length_field: torch.tensor(item_list_length),
            'is_cold': []
        }

        counter_dict = self.item_counter
        '按值，升序排序↑'
        sorted_counter_tuple_list = sorted(counter_dict.items(), key=lambda x:x[1])
        item_set_num = len(sorted_counter_tuple_list)
        long_tail_rate = self.config['long_tail_rate']
        if long_tail_rate == 0:
            long_tail_item_idx = 0
        else:
            long_tail_item_idx = int(item_set_num*long_tail_rate)-1
        cold_item_list = []
        for long_tail_item in sorted_counter_tuple_list[:long_tail_item_idx]:
            item_is_cold_tuple = (long_tail_item[0],1)
            cold_item_list.append(item_is_cold_tuple)
        for hot_item in sorted_counter_tuple_list[long_tail_item_idx:]:
            item_is_hot_tuple = (hot_item[0],0)
            cold_item_list.append(item_is_hot_tuple)
        item_dict = dict(cold_item_list)
        '按键，升序排序↑     注意有没有0'
        sorted_item_tuple_list = sorted(item_dict.items(), key=lambda x:x[0])
        sorted_item_tuple_list.insert(0, (0, 0))
        is_cold_list = [item_tuple[1] for item_tuple in sorted_item_tuple_list]
        is_cold_list = torch.tensor(is_cold_list)

        for field in self.inter_feat:
            if field != self.uid_field:
                list_field = getattr(self, f'{field}_list_field')
                list_len = self.field2seqlen[list_field]
                shape = (new_length, list_len) if isinstance(list_len, int) else (new_length,) + list_len
                new_dict[list_field] = torch.zeros(shape, dtype=self.inter_feat[field].dtype)

                value = self.inter_feat[field]
                if field in ['item_id', 'business_id']:
                    new_dict['is_cold'] = torch.zeros(shape, dtype=torch.int64)

                    for i, (index, length) in enumerate(zip(item_list_index, item_list_length)):
                        new_dict[list_field][i][:length] = value[index]
                        new_dict['is_cold'][i][:length] = is_cold_list[value[index]]
                else:
                    for i, (index, length) in enumerate(zip(item_list_index, item_list_length)):
                        new_dict[list_field][i][:length] = value[index]

        new_data.update(Interaction(new_dict))
        self.inter_feat = new_data

    # def data_augmentation(self):
    #     """Augmentation processing for sequential dataset.
    #
    #     E.g., ``u1`` has purchase sequence ``<i1, i2, i3, i4>``,
    #     then after augmentation, we will generate three cases.
    #
    #     ``u1, <i1> | i2``
    #
    #     (Which means given user_id ``u1`` and item_seq ``<i1>``,
    #     we need to predict the next item ``i2``.)
    #
    #     The other cases are below:
    #
    #     ``u1, <i1, i2> | i3``
    #
    #     ``u1, <i1, i2, i3> | i4``
    #     """
    #     self.logger.debug('data_augmentation')
    #
    #     self._aug_presets()
    #
    #     self._check_field('uid_field', 'time_field')
    #     max_item_list_len = self.config['MAX_ITEM_LIST_LENGTH']
    #     data_augmentation_flag = False if self.config['data_augmentation'] is None else self.config['data_augmentation']
    #     self.sort(by=[self.uid_field, self.time_field], ascending=True)
    #     self.config['save_for_clea'] = False
    #     if self.config['save_for_clea'] is not None and self.config['save_for_clea']:
    #         dataset_dict = {}
    #         uid = self.inter_feat[self.uid_field]
    #         iid = self.inter_feat[self.iid_field].numpy()
    #         for i, uid in enumerate(uid.numpy()):
    #             if uid not in dataset_dict.keys():
    #                 dataset_dict[uid] = [[iid[i]]]
    #             else:
    #                 dataset_dict[uid].append([iid[i]])
    #
    #         path = self.dataset_path
    #         if not os.path.exists(path):
    #             makedirs(path)
    #         path += '/' + self.config['dataset'] + '.txt'
    #         f = open(self.dataset_path + '/' + self.config['dataset'] + '.txt', 'w')
    #         f.write(str(dataset_dict))
    #         f.close()
    #
    #     last_uid = None
    #     uid_list, item_list_index, target_index, item_list_length = [], [], [], []
    #     uid_list_no_augment, item_list_index_no_augment, target_index_no_augment, item_list_length_no_augment = [], [], [], []
    #     seq_start = 0
    #     for i, uid in enumerate(self.inter_feat[self.uid_field].numpy()):
    #         if last_uid != uid:
    #             if last_uid is not None:
    #                 uid_list_no_augment.append(uid_list[-1])
    #                 item_list_index_no_augment.append(item_list_index[-1])
    #                 target_index_no_augment.append(target_index[-1])
    #                 item_list_length_no_augment.append(item_list_length[-1])
    #             last_uid = uid
    #             seq_start = i
    #         else:
    #             if i - seq_start > max_item_list_len:
    #                 seq_start += 1
    #             uid_list.append(uid)
    #             item_list_index.append(slice(seq_start, i))
    #             target_index.append(i)
    #             item_list_length.append(i - seq_start)
    #
    #     uid_list_no_augment.append(uid_list[-1])
    #     item_list_index_no_augment.append(item_list_index[-1])
    #     target_index_no_augment.append(target_index[-1])
    #     item_list_length_no_augment.append(item_list_length[-1])
    #
    #     if data_augmentation_flag:
    #         uid_list = uid_list_no_augment
    #         item_list_index = item_list_index_no_augment
    #         target_index = target_index_no_augment
    #         item_list_length = item_list_length_no_augment
    #
    #     uid_list = np.array(uid_list)
    #     item_list_index = np.array(item_list_index)
    #     target_index = np.array(target_index)
    #     item_list_length = np.array(item_list_length, dtype=np.int64)
    #
    #     new_length = len(item_list_index)
    #     new_data = self.inter_feat[target_index]
    #     new_dict = {
    #         self.item_list_length_field: torch.tensor(item_list_length),
    #     }
    #
    #     for field in self.inter_feat:
    #         if field != self.uid_field:
    #             list_field = getattr(self, f'{field}_list_field')
    #             list_len = self.field2seqlen[list_field]
    #             shape = (new_length, list_len) if isinstance(list_len, int) else (new_length,) + list_len
    #             new_dict[list_field] = torch.zeros(shape, dtype=self.inter_feat[field].dtype)
    #
    #             value = self.inter_feat[field]
    #             for i, (index, length) in enumerate(zip(item_list_index, item_list_length)):
    #                 new_dict[list_field][i][:length] = value[index]
    #
    #     new_data.update(Interaction(new_dict))
    #     self.inter_feat = new_data
    def _benchmark_presets(self):
        list_suffix = self.config['LIST_SUFFIX']
        for field in self.inter_feat:
            if field + list_suffix in self.inter_feat:
                list_field = field + list_suffix
                setattr(self, f'{field}_list_field', list_field)
        self.set_field_property(self.item_list_length_field, FeatureType.TOKEN, FeatureSource.INTERACTION, 1)
        self.inter_feat[self.item_list_length_field] = self.inter_feat[self.item_id_list_field].agg(len)

    def inter_matrix(self, form='coo', value_field=None):
        """Get sparse matrix that describe interactions between user_id and item_id.
        Sparse matrix has shape (user_num, item_num).
        For a row of <src, tgt>, ``matrix[src, tgt] = 1`` if ``value_field`` is ``None``,
        else ``matrix[src, tgt] = self.inter_feat[src, tgt]``.

        Args:
            form (str, optional): Sparse matrix format. Defaults to ``coo``.
            value_field (str, optional): Data of sparse matrix, which should exist in ``df_feat``.
                Defaults to ``None``.

        Returns:
            scipy.sparse: Sparse matrix in form ``coo`` or ``csr``.
        """
        if not self.uid_field or not self.iid_field:
            raise ValueError('dataset does not exist uid/iid, thus can not converted to sparse matrix.')

        l1_idx = (self.inter_feat[self.item_list_length_field] == 1)
        l1_inter_dict = self.inter_feat[l1_idx].interaction
        new_dict = {}
        list_suffix = self.config['LIST_SUFFIX']
        candidate_field_set = set()
        for field in l1_inter_dict:
            if field != self.uid_field and field + list_suffix in l1_inter_dict:
                candidate_field_set.add(field)
                new_dict[field] = torch.cat([self.inter_feat[field], l1_inter_dict[field + list_suffix][:, 0]])
            elif (not field.endswith(list_suffix)) and (field != self.item_list_length_field):
                new_dict[field] = torch.cat([self.inter_feat[field], l1_inter_dict[field]])
        local_inter_feat = Interaction(new_dict)
        return self._create_sparse_matrix(local_inter_feat, self.uid_field, self.iid_field, form, value_field)

    def build(self):
        """Processing dataset according to evaluation setting, including Group, Order and Split.
        See :class:`~recbole.config.eval_setting.EvalSetting` for details.

        Args:
            eval_setting (:class:`~recbole.config.eval_setting.EvalSetting`):
                Object contains evaluation settings, which guide the data processing procedure.

        Returns:
            list: List of built :class:`Dataset`.
        """
        ordering_args = self.config['eval_args']['order']
        if ordering_args != 'TO':
            raise ValueError(f'The ordering args for sequential recommendation has to be \'TO\'')

        return super().build()
