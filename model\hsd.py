from HsdUtils.utils import get_model  # 导入自定义工具函数，用于获取模型类
import torch  # 导入PyTorch主库
from torch import nn  # 导入神经网络模块
import torch.nn.functional as F  # 导入函数式API
from recbole.model.abstract_recommender import SequentialRecommender  # 导入RecBole的序列推荐基类
from torch.nn.init import xavier_uniform_, xavier_normal_  # 导入权重初始化函数


class HSD(SequentialRecommender):
    """
    层次化项目不一致性信号学习模型(Hierarchical Item Inconsistency Signal Learning)
    用于序列推荐中的序列去噪，通过层次化的方式学习项目不一致性信号，识别并过滤噪声项目
    """
    def __init__(self, config, dataset):
        """
        初始化HSD模型

        Args:
            config (Config): 配置对象，包含模型参数
            dataset (Dataset): 数据集对象，包含数据集信息
        """
        super(HSD, self).__init__(config, dataset)  # 调用父类初始化方法

        # 加载参数信息
        self.hidden_size = config['hidden_size']  # 隐藏层大小，与嵌入维度相同
        self.our_ae_drop_out = config['our_ae_drop_out']  # 自编码器dropout率
        self.our_att_drop_out = config['our_att_drop_out']  # 注意力机制dropout率
        self.n_users = dataset.num(self.USER_ID)  # 用户数量，兼容RecBole最新版本

        self.tau = 100  # Gumbel-Softmax温度参数初始值
        self.filter_drop_rate = 0.0  # 过滤丢弃率初始值

        # 用户嵌入层，padding_idx=0表示id为0的用户使用零向量表示
        self.user_embedding = nn.Embedding(self.n_users, self.hidden_size, padding_idx=0)

        # 批归一化层，用于嵌入向量的归一化
        self.LayerNorm = nn.BatchNorm1d(self.hidden_size)
        # Dropout层，用于防止过拟合
        self.emb_dropout = nn.Dropout(self.our_ae_drop_out)

        # 双向LSTM层，用于序列级一致性检测
        self.rnn = nn.LSTM(
            input_size=self.hidden_size,  # 输入维度为嵌入维度
            hidden_size=self.hidden_size,  # 隐藏状态维度
            bidirectional=True,  # 使用双向LSTM
            batch_first=True  # 输入张量的第一维是批次大小
        )

        # 2D卷积层，用于处理原始序列和重构序列的拼接
        self.conv = nn.Conv2d(self.max_seq_length, self.max_seq_length, (1, 2))
        # 序列级MLP，输出2维分数（保留/丢弃）
        self.seq_level_mlp = nn.Sequential(
            nn.Linear(self.hidden_size, 2, bias=False),  # 线性层，输出2维
            nn.Sigmoid()  # Sigmoid激活函数，将输出压缩到(0,1)
        )

        # 注意力读出层，用于项目级一致性检测
        self.read_out = AttnReadout(
            input_dim=self.hidden_size,  # 输入维度
            hidden_dim=self.hidden_size,  # 隐藏层维度
            output_dim=self.hidden_size,  # 输出维度
            session_len=self.max_seq_length,  # 会话最大长度
            batch_norm=True,  # 使用批归一化
            feat_drop=self.our_att_drop_out,  # 特征dropout率
            activation=nn.PReLU(self.hidden_size),  # 使用PReLU激活函数
        )

        # Sigmoid激活函数
        self.sigmoid = nn.Sigmoid()

        # Softmax激活函数，dim=1表示在第二维上进行softmax
        self.softmax = nn.Softmax(dim=1)
        # 二元Softmax，在最后一维上进行softmax
        self.binary_softmax = nn.Softmax(dim=-1)

        # 交叉熵损失函数，用于预测损失
        self.loss_fuc = nn.CrossEntropyLoss()

        # 应用权重初始化函数
        self.apply(self._init_weights)

        # 初始化子模型（基础序列推荐模型）
        self.sub_model = get_model(config['sub_model'])(config, dataset).to(config['device'])
        self.sub_model_name = config['sub_model']  # 保存子模型名称
        self.item_embedding = self.sub_model.item_embedding  # 使用子模型的项目嵌入

        # 如果配置了加载预训练嵌入
        if config['load_pre_train_emb'] is not None and config['load_pre_train_emb']:
            # 获取预训练模型文件路径
            checkpoint_file = config['pre_train_model_dict'][config['dataset']][config['sub_model']]
            # 加载预训练模型
            checkpoint = torch.load(checkpoint_file)
            # 根据子模型类型加载不同的嵌入权重
            if config['sub_model'] == 'DSAN':
                embedding_weight = checkpoint['state_dict']['embedding.weight']
                self.item_embedding = nn.Embedding.from_pretrained(embedding_weight, freeze=False)
            else:
                item_embedding_weight = checkpoint['state_dict']['item_embedding.weight']
                self.item_embedding = nn.Embedding.from_pretrained(item_embedding_weight, freeze=False)

    def _init_weights(self, module):
        """
        初始化模型权重

        Args:
            module: 需要初始化的模块
        """
        if isinstance(module, (nn.Linear, nn.Embedding)):
            # 对线性层和嵌入层使用xavier_normal_初始化
            xavier_normal_(module.weight)
        elif isinstance(module, nn.LayerNorm):
            # 对层归一化层，偏置初始化为0，权重初始化为1
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            # 对线性层的偏置初始化为0
            module.bias.data.zero_()
        elif isinstance(module, nn.LSTM):
            # 对LSTM的权重使用xavier_uniform_初始化
            xavier_uniform_(module.weight_hh_l0)  # 隐藏状态到隐藏状态的权重
            xavier_uniform_(module.weight_ih_l0)  # 输入到隐藏状态的权重

    def method_name(self, generated_seq, generated_seq_emb):
        """
        重新排列去噪后的序列，将非零元素移到序列前面

        Args:
            generated_seq: 去噪后的序列，可能包含零元素
            generated_seq_emb: 去噪后的序列嵌入

        Returns:
            denoising_seq: 重排后的序列
            denoising_seq_emb: 重排后的序列嵌入
        """
        # 找出所有非零元素的位置
        row_indexes, col_id = torch.where(generated_seq.gt(0))
        row_flag = row_indexes[0]  # 记录当前行索引
        index_flag = -1  # 列索引计数器
        col_index = []  # 存储新的列索引

        # 为每个非零元素计算新的列索引
        for row_index in row_indexes:
            if row_index == row_flag:
                # 如果还在同一行，列索引加1
                index_flag += 1
                col_index.append(index_flag)
            else:
                # 如果换到新行，列索引重置为0
                index_flag = 0
                col_index.append(index_flag)
                row_flag = row_index

        # 转换为张量
        col_index = torch.tensor(col_index, device=self.device)

        # 创建新的序列和嵌入张量
        denoising_seq = torch.zeros_like(generated_seq)
        denoising_seq_emb = torch.zeros_like(generated_seq_emb)

        # 将非零元素放到新位置
        denoising_seq[row_indexes, col_index] = generated_seq[row_indexes, col_id]
        denoising_seq_emb[row_indexes, col_index, :] = generated_seq_emb[row_indexes, col_id, :]

        return denoising_seq, denoising_seq_emb

    def generate_pos_seq(self, item_seq, item_seq_len, item_level_score, seq_level_score, mask):
        """
        生成去噪后的正样本序列

        Args:
            item_seq: 原始项目序列
            item_seq_len: 序列长度
            item_level_score: 项目级评分
            seq_level_score: 序列级评分
            mask: 掩码，标记有效位置

        Returns:
            pos_seq: 去噪后的序列
            pos_seq_emb: 去噪后的序列嵌入
            pos_seq_len: 去噪后的序列长度
            item_seq: 原始序列（作为负样本）
            neg_seq_len: 原始序列长度
            clean_seq_percent: 清洁序列百分比
        """
        # 获取项目嵌入
        item_emb = self.item_embedding(item_seq)
        mask = mask.squeeze()  # 去除维度为1的维度

        # 使用Gumbel-Softmax进行可微分的离散采样
        # hard=True表示在前向传播时使用离散的one-hot向量，反向传播时使用软概率
        item_level_gumbel_softmax_rst = F.gumbel_softmax(item_level_score, tau=self.tau, hard=True)
        seq_level_gumbel_softmax_rst = F.gumbel_softmax(seq_level_score, tau=self.tau, hard=True)

        # 提取项目级和序列级的噪声标志（取第二维，表示"丢弃"的概率）
        item_level_denoising_seq_flag = item_level_gumbel_softmax_rst[:, :, 1] * mask
        seq_level_denoising_seq_flag = seq_level_gumbel_softmax_rst[:, :, 1] * mask

        # 层次化决策：两个级别都认为是噪声才标记为噪声
        noisy_flag = item_level_denoising_seq_flag * seq_level_denoising_seq_flag

        # 生成正样本标志（非噪声项）
        pos_flag = (1 - noisy_flag) * mask

        # 生成正样本序列嵌入
        pos_seq_emb = item_emb * pos_flag.unsqueeze(-1)

        # 生成正样本序列
        pos_seq = item_seq * pos_flag
        pos_seq[pos_seq != pos_seq] = 0  # 将NaN值设为0

        # 计算正样本序列长度
        pos_seq_len = torch.sum(pos_flag, dim=-1)

        # 如果序列长度为0，保留第一个元素，防止STAMP模型报错
        pos_seq_len[pos_seq_len.eq(0)] = 1

        # 计算清洁序列百分比
        clean_seq_percent = torch.sum(pos_seq_len, dim=0) / item_seq_len.sum() * 100

        # 重排序列，将非零元素移到前面
        denoising_seq, denoising_seq_emb = self.method_name(pos_seq, pos_seq_emb)
        pos_seq = denoising_seq
        pos_seq_emb = denoising_seq_emb

        # 获取负样本序列长度
        neg_seq_len = torch.squeeze(item_seq_len)

        return pos_seq, pos_seq_emb, pos_seq_len.long(), item_seq, neg_seq_len, clean_seq_percent

    def seq_level_consistency(self, item_seq, item_seq_len, mask, train_flag=True):
        """
        序列级一致性检测，使用双向LSTM进行序列重构

        Args:
            item_seq: 项目序列
            item_seq_len: 序列长度
            mask: 掩码，标记有效位置
            train_flag: 是否处于训练模式

        Returns:
            element_wise_reconstruction_loss: 重构损失
            reconstruct_score: 序列级评分
            encoder_item_seq_emb: 编码器输出的序列嵌入
            encoder_hidden: 编码器的隐藏状态
        """
        # 获取原始项目序列嵌入
        item_seq_emb_ori = self.item_embedding(item_seq)

        # 应用dropout并使用掩码
        item_seq_emb = self.emb_dropout(item_seq_emb_ori) * mask

        # 使用双向LSTM编码序列
        encoder_item_seq_emb_bi_direction, (encoder_hidden, mm_) = self.rnn(item_seq_emb)

        # 将最后一个时刻的hidden state两个方向加起来
        encoder_hidden = (encoder_hidden[0] + encoder_hidden[1]).squeeze()

        # 合并双向LSTM的输出（前向和后向）
        # 形状为[batch_size, seq_len, 2*hidden_size]
        rnn1_hidden = int(encoder_item_seq_emb_bi_direction.shape[-1] / 2)
        encoder_item_seq_emb = encoder_item_seq_emb_bi_direction[:, :, :rnn1_hidden] + \
                               encoder_item_seq_emb_bi_direction[:, :, rnn1_hidden:]

        # 应用掩码
        encoder_item_seq_emb = encoder_item_seq_emb * mask

        # 使用双向LSTM解码序列（重构）
        decoder_item_seq_emb_bi_direction, _ = self.rnn(encoder_item_seq_emb)

        # 合并解码器输出
        rnn2_hidden = int(decoder_item_seq_emb_bi_direction.shape[-1] / 2)
        decoder_item_seq_emb = decoder_item_seq_emb_bi_direction[:, :, :rnn2_hidden] + \
                               decoder_item_seq_emb_bi_direction[:, :, rnn2_hidden:]

        # 初始化重构损失
        element_wise_reconstruction_loss = 0

        # 如果是训练模式，计算重构损失
        if train_flag:
            loss_fct = nn.MSELoss(reduction='none')  # 使用不归约的MSE损失
            # 计算重构损失，并按序列长度归一化
            element_wise_reconstruction_loss = loss_fct(decoder_item_seq_emb * mask, item_seq_emb_ori * mask).sum(
                -1).sum(-1) / item_seq_len.squeeze()

        # 将重构序列和原始序列在最后一维拼接
        # 形状为[batch_size, seq_len, hidden_size, 2]
        concat_shuffled_and_origin = torch.stack((decoder_item_seq_emb, item_seq_emb_ori), dim=-1)

        # 使用2D卷积处理拼接后的序列
        # 输出形状为[batch_size, seq_len, hidden_size, 1]
        concat_shuffled_and_origin = self.conv(concat_shuffled_and_origin)

        # 去除最后一维，形状为[batch_size, seq_len, hidden_size]
        concat_shuffled_and_origin = torch.squeeze(concat_shuffled_and_origin)

        # 应用dropout
        concat_shuffled_and_origin = self.emb_dropout(concat_shuffled_and_origin)

        # 应用ReLU激活函数
        concat_shuffled_and_origin = nn.ReLU(inplace=True)(concat_shuffled_and_origin)

        # 使用MLP生成序列级评分，形状为[batch_size, seq_len, 2]
        reconstruct_score = self.seq_level_mlp(concat_shuffled_and_origin).squeeze()

        # 应用掩码
        reconstruct_score = reconstruct_score * mask

        return element_wise_reconstruction_loss, reconstruct_score, encoder_item_seq_emb, encoder_hidden

    def item_level_consistency(self, item_seq_emb, target_embedding, mask):
        """
        项目级一致性检测，使用注意力机制计算项目与目标的相关性

        Args:
            item_seq_emb: 项目序列嵌入
            target_embedding: 目标项目嵌入
            mask: 掩码，标记有效位置

        Returns:
            item_level_score: 项目级评分
            item_level_long_term_representation: 项目级长期表示
            item_level_seq_emb: 项目级序列嵌入
        """
        # 使用注意力读出层计算项目级评分和表示
        item_level_score, item_level_long_term_representation, item_level_seq_emb = self.read_out(
            item_seq_emb,  # 项目序列嵌入
            target_embedding,  # 目标项目嵌入
            mask  # 掩码
        )

        return item_level_score, item_level_long_term_representation, item_level_seq_emb

    def loss_filter(self, user, item_seq, item_seq_len, interaction, mask, train_flag):
        """
        基于损失的过滤机制，用于识别难以学习的样本

        Args:
            user: 用户ID
            item_seq: 项目序列
            item_seq_len: 序列长度
            interaction: 交互数据
            mask: 掩码
            train_flag: 是否处于训练模式

        Returns:
            loss: 过滤后的损失
            loss_filter_flag: 过滤标志
        """
        # 获取项目序列嵌入，形状为[B, L, H]
        item_seq_emb = self.item_embedding(item_seq)
        # 应用掩码并增加维度，形状为[B, L, 1, H]
        item_seq_emb = (item_seq_emb * mask).unsqueeze(-2)
        # 获取用户嵌入并调整形状，形状为[B, 1, H, 1]
        user_emb = self.user_embedding(user).unsqueeze(-2).unsqueeze(-1)

        # 计算正样本得分，形状为[B, L, 1]
        pos_score = torch.matmul(item_seq_emb, user_emb)

        # 获取过滤丢弃率
        filter_drop_rate = self.filter_drop_rate

        if train_flag:
            # 训练模式下，获取负样本
            neg_items = interaction[self.NEG_ITEM_ID]
            # 扩展负样本到序列形状
            neg_items = neg_items.unsqueeze(-1).expand(item_seq.shape)
            # 获取负样本嵌入，形状为[B, L, H]
            neg_items_emb = self.item_embedding(neg_items)
            # 应用掩码并调整形状，形状为[B, L, 1, H]
            neg_items_emb = (neg_items_emb * mask).unsqueeze(-2)
            # 计算负样本得分，形状为[B, L, 1]
            neg_score = torch.matmul(neg_items_emb, user_emb)
        else:
            # 测试模式下，负样本得分为0
            neg_score = torch.zeros_like(pos_score)
            filter_drop_rate = 0.2  # 测试时使用固定的丢弃率

        # 计算BPR损失（Bayesian Personalized Ranking）
        loss = -torch.log(1e-10 + torch.sigmoid(pos_score - neg_score)).squeeze(-1).squeeze(-1)
        # 应用掩码
        loss = loss * mask.squeeze(-1)

        # 按损失值排序，升序排列（损失小的在前）
        loss_sorted, ind_sorted = torch.sort(loss, descending=False, dim=-1)
        # 计算需要过滤的项目数量
        num_remember = (filter_drop_rate * item_seq_len).squeeze(-1).long()

        # 初始化过滤标志
        loss_filter_flag = torch.zeros_like(item_seq)

        # 为每个序列设置过滤标志
        for index, filtered_item_num in enumerate(num_remember):
            # 获取损失最大的项目索引（需要过滤的项目）
            loss_index = ind_sorted[index][-filtered_item_num:]
            # 设置过滤标志
            loss_filter_flag[index][loss_index] = 1
            if filter_drop_rate != 0:
                # 将被过滤项目的损失设为0
                loss[index][loss_index] *= 0

        # 应用掩码到过滤标志
        loss_filter_flag = loss_filter_flag * mask.squeeze(-1)
        return loss, loss_filter_flag

    def forward(self, interaction, train_flag=True):
        """
        模型前向传播

        Args:
            interaction: 交互数据
            train_flag: 是否处于训练模式

        Returns:
            element_wise_reconstruction_loss: 重构损失
            pos_seq: 去噪后的正样本序列
            pos_seq_emb: 去噪后的正样本序列嵌入
            pos_seq_len: 去噪后的序列长度
            neg_seq: 原始序列（负样本）
            neg_seq_len: 原始序列长度
            embedding_level_denoising_emb: 嵌入级去噪表示
            clean_seq_percent: 清洁序列百分比
            loss: 平均损失
        """
        # 从交互数据中提取序列信息
        item_seq = interaction[self.ITEM_SEQ]  # 项目序列
        item_seq_len = interaction[self.ITEM_SEQ_LEN].unsqueeze(1)  # 序列长度

        if train_flag:
            # 训练模式下，目标项目来自标签
            target_item = interaction[self.ITEM_ID].unsqueeze(1)
        else:
            # 验证和测试时，由于不能提前预知下一项，将序列的最后一项视作目标
            target_item = item_seq.gather(1, item_seq_len - 1)

        user = interaction[self.USER_ID]  # 用户ID

        # 计算掩码，标记有效位置（非零项目）
        mask = torch.ones(item_seq.shape, dtype=torch.float, device=item_seq.device) * item_seq.gt(0)
        # 增加维度，形状为[batch_size, seq_len, 1]
        mask = mask.unsqueeze(2)

        # 获取项目序列嵌入
        item_seq_emb = self.item_embedding(item_seq)

        # 序列级一致性检测
        element_wise_reconstruction_loss, seq_level_score, seq_level_encoder_item_emb, seq_level_seq_emb = self.seq_level_consistency(
            item_seq=item_seq,
            item_seq_len=item_seq_len,
            mask=mask,
            train_flag=train_flag
        )

        # 根据模式选择目标嵌入
        if train_flag:
            # 训练模式使用目标项目嵌入
            target_embedding = self.item_embedding(target_item)
        else:
            # 测试模式使用用户嵌入
            target_embedding = self.user_embedding(user.unsqueeze(-1))

        # 项目级一致性检测
        item_level_score, item_level_long_term_representation, item_level_seq_emb = self.item_level_consistency(
            item_seq_emb=item_seq_emb,
            target_embedding=target_embedding,
            mask=mask)

        # 将项目级和序列级的嵌入作为嵌入级去噪，增强数据级去噪效果
        embedding_level_denoising_emb = item_level_seq_emb + seq_level_seq_emb

        # 基于损失的过滤
        loss, loss_filter_flag = self.loss_filter(user, item_seq, item_seq_len, interaction, mask, train_flag)

        # 生成去噪后的正样本序列
        pos_seq, pos_seq_emb, pos_seq_len, neg_seq, neg_seq_len, clean_seq_percent = self.generate_pos_seq(
            item_seq=item_seq,
            item_seq_len=item_seq_len,
            item_level_score=item_level_score,
            seq_level_score=seq_level_score,
            mask=mask,
        )

        return element_wise_reconstruction_loss, pos_seq, pos_seq_emb, pos_seq_len, neg_seq, neg_seq_len, embedding_level_denoising_emb, clean_seq_percent, loss.sum(-1).mean()

    def calculate_loss_curriculum(self, interaction, drop_rate, tau):
        """
        使用课程学习策略计算损失

        Args:
            interaction: 交互数据
            drop_rate: 丢弃率，用于课程学习
            tau: Gumbel-Softmax温度参数

        Returns:
            total_loss: 总损失
            clean_seq_percent: 清洁序列百分比
            L_rec: 重构损失
            generated_seq_loss: 生成序列损失
        """
        # 更新温度参数和过滤丢弃率
        self.tau = tau
        self.filter_drop_rate = 0.2 - drop_rate  # 随着训练进行，过滤率逐渐降低

        # 前向传播获取各种输出
        element_wise_reconstruction_loss, pos_seq, pos_seq_emb, pos_seq_len, neg_seq, neg_seq_len, embedding_level_denoising_emb, clean_seq_percent, loss_filter_loss = self.forward(interaction)

        # 注释说明：
        # positive seq                  --- A (去噪后的正样本序列)
        # embedding_level_denoising_emb --- B (嵌入级去噪表示)
        # negative seq                  --- C (原始序列，作为负样本)
        # 目标：min/max D(AB)-D(AC) (最小化正样本距离，最大化负样本距离)

        # 根据子模型类型决定是否需要用户信息
        user = interaction[self.USER_ID] if self.sub_model_name == 'Caser' else None
        # 获取所有项目嵌入（删除掩码token）
        all_items_emb = self.item_embedding.weight[:self.n_items]

        # 使用去噪后的嵌入计算预测损失
        sub_model_output = self.sub_model_forward(pos_seq, pos_seq_emb, pos_seq_len, user)
        seq_representation, delete_index = self.denoising_seq_gather(pos_seq, sub_model_output)
        # 计算所有项目的得分，形状为[B, item_num]
        scores = torch.matmul(seq_representation, all_items_emb.transpose(0, 1))

        # 课程学习：选择容易学习的样本
        ind_update = self.cal_curriculum_batch_id(drop_rate, element_wise_reconstruction_loss)
        # 只对选中的样本计算重构损失
        element_wise_reconstruction_loss_curriculum = element_wise_reconstruction_loss[ind_update]
        L_rec = element_wise_reconstruction_loss_curriculum.mean()

        # 获取目标项目
        target_item = interaction[self.ITEM_ID].unsqueeze(1)
        target_item = target_item.squeeze()
        # 计算生成序列的预测损失（只对选中的样本）
        generated_seq_loss = self.loss_fuc(scores[ind_update], target_item[ind_update])

        # 总损失 = 重构损失 + 预测损失 + 过滤损失
        total_loss = L_rec + generated_seq_loss + loss_filter_loss
        return total_loss, clean_seq_percent, L_rec, generated_seq_loss

    def cal_curriculum_batch_id(self, drop_rate, element_wise_reconstruction_loss):
        """
        课程学习：根据重构损失选择容易学习的样本

        Args:
            drop_rate: 丢弃率
            element_wise_reconstruction_loss: 逐元素重构损失

        Returns:
            ind_update: 选中样本的索引
        """
        # 按重构损失升序排序（损失小的在前，表示容易学习）
        loss_sorted, ind_sorted = torch.sort(element_wise_reconstruction_loss, descending=False)
        # 计算保留率
        remember_rate = 1 - drop_rate
        # 计算保留的样本数量
        num_remember = int(remember_rate * len(loss_sorted))
        # 选择损失最小的样本（最容易学习的）
        ind_update = ind_sorted[:num_remember]
        return ind_update

    def predict(self, interaction):
        """
        预测函数，用于计算特定项目的得分

        Args:
            interaction: 交互数据

        Returns:
            scores: 预测得分
        """
        # 根据子模型类型决定是否需要用户信息
        user = interaction[self.USER_ID] if self.sub_model_name == 'Caser' else None

        # 前向传播（测试模式）
        element_wise_reconstruction_loss, pos_seq, generated_seq, denoising_seq_len, temp1_, temp2_, embedding_level_denoising_emb, clean_seq_percent, loss = self.forward(
            interaction,
            train_flag=False)

        # 使用子模型进行前向传播
        seq_output = self.sub_model_forward(generated_seq, denoising_seq_len, user)

        # 聚合去噪序列的输出
        seq_output, _ = self.denoising_seq_gather(generated_seq, seq_output)

        # 获取测试项目和其嵌入
        test_item = interaction[self.ITEM_ID]
        test_item_emb = self.item_embedding(test_item)

        # 计算得分（序列表示与项目嵌入的点积）
        scores = torch.mul(seq_output, test_item_emb).sum(dim=1)  # [B]
        return scores

    def denoising_seq_gather(self, generated_seq, seq_output):
        """
        聚合去噪序列的输出，根据不同的子模型采用不同的聚合策略

        Args:
            generated_seq: 生成的去噪序列
            seq_output: 子模型的输出

        Returns:
            seq_output: 聚合后的序列输出
            delete_index: 需要删除的索引（全为噪声的序列）
        """
        # 计算生成序列的实际长度（非零元素个数）
        generated_item_seq_len = torch.sum(generated_seq.gt(0), 1)

        # 找出长度为0的序列（全为噪声项的序列），记录其索引
        delete_index = torch.where(generated_item_seq_len.eq(0))[0]

        # 将长度减1，防止数组越界（因为索引从0开始）
        generated_item_seq_len = generated_item_seq_len - 1

        # 将长度为-1的项置为0，防止数组越下界
        generated_item_seq_len = generated_item_seq_len * generated_item_seq_len.gt(0)

        # 根据不同的子模型采用不同的输出聚合策略
        if self.sub_model_name in ['SRGNN', 'GCSAN', 'Caser', 'NARM', 'DSAN', 'STAMP']:
            # 这些模型已经输出了聚合后的表示，直接使用
            seq_output = seq_output
        elif self.sub_model_name == 'fmlp':
            # FMLP模型取最后一个位置的输出
            seq_output = seq_output[:, -1, :]  # 删除掩码token
        else:
            # 其他模型（如BERT4Rec, SASRec等）需要根据序列长度聚合
            seq_output = self.gather_indexes(seq_output, generated_item_seq_len)  # [B, H]

        return seq_output, delete_index

    def sub_model_forward(self, generated_seq, pos_seq_emb, denoising_seq_len, user):
        """
        子模型前向传播，根据不同的子模型调用相应的前向函数

        Args:
            generated_seq: 生成的去噪序列
            pos_seq_emb: 正样本序列嵌入
            denoising_seq_len: 去噪序列长度
            user: 用户ID（某些模型需要）

        Returns:
            seq_output: 子模型的输出
        """
        if self.sub_model_name == 'BERT4Rec':
            # BERT4Rec模型的去噪前向传播
            seq_output = self.sub_model.forward_denoising(generated_seq, pos_seq_emb)
        elif self.sub_model_name == 'GRU4Rec':
            # GRU4Rec模型的去噪前向传播
            seq_output = self.sub_model.forward_denoising(generated_seq, pos_seq_emb)
        elif self.sub_model_name == 'SASRec':
            # SASRec模型的去噪前向传播
            seq_output = self.sub_model.forward_denoising(generated_seq, pos_seq_emb)
        elif self.sub_model_name == 'Caser':
            # Caser模型需要用户信息
            seq_output = self.sub_model.forward_denoising(user, generated_seq, pos_seq_emb)
        elif self.sub_model_name == 'NARM':
            # NARM模型需要序列长度信息
            seq_output = self.sub_model.forward_denoising(generated_seq, denoising_seq_len, pos_seq_emb)
        elif self.sub_model_name == 'DSAN':
            # DSAN模型使用标准前向传播
            seq_output, _ = self.sub_model.forward(generated_seq)
        elif self.sub_model_name == 'fmlp':
            # FMLP模型使用标准前向传播
            seq_output = self.sub_model.forward(generated_seq)
        elif self.sub_model_name == 'STAMP':
            # STAMP模型的去噪前向传播
            seq_output = self.sub_model.forward_denoising(generated_seq, denoising_seq_len, pos_seq_emb)
        else:
            # 不支持的子模型
            raise ValueError(f'Sub_model [{self.sub_model_name}] not support.')
        return seq_output

    def full_sort_predict(self, interaction):
        """
        全排序预测，计算对所有项目的得分

        Args:
            interaction: 交互数据

        Returns:
            scores: 对所有项目的得分
            pre: 清洁序列百分比
        """
        # 根据子模型类型决定是否需要用户信息
        user = interaction[self.USER_ID] if self.sub_model_name == 'Caser' else None

        # 前向传播（测试模式）
        element_wise_reconstruction_loss, pos_seq, pos_seq_emb, denoising_seq_len, _, _, embedding_level_denoising_emb, pre, loss = self.forward(
            interaction,
            train_flag=False)

        # 使用子模型进行前向传播
        seq_output = self.sub_model_forward(pos_seq, pos_seq_emb, denoising_seq_len, user)

        # 聚合去噪序列的输出
        seq_output, _ = self.denoising_seq_gather(pos_seq, seq_output)

        # 获取所有项目的嵌入（删除掩码token）
        test_items_emb = self.item_embedding.weight[:self.n_items]

        # 计算对所有项目的得分
        scores = torch.matmul(seq_output, test_items_emb.transpose(0, 1))  # [B, item_num]
        return scores, pre


class AttnReadout(nn.Module):
    """
    注意力读出层，用于项目级一致性检测
    通过注意力机制计算每个项目与目标的相关性，并生成长期和短期表示
    """
    def __init__(
            self,
            input_dim,      # 输入维度
            hidden_dim,     # 隐藏层维度
            output_dim,     # 输出维度
            session_len,    # 会话长度
            batch_norm=True,    # 是否使用批归一化
            feat_drop=0.0,      # 特征dropout率
            activation=None,    # 激活函数
    ):
        """
        初始化注意力读出层

        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            output_dim: 输出维度
            session_len: 会话最大长度
            batch_norm: 是否使用批归一化
            feat_drop: 特征dropout率
            activation: 激活函数
        """
        super().__init__()
        # 批归一化层（可选）
        self.batch_norm = nn.BatchNorm1d(session_len) if batch_norm else None
        # Dropout层
        self.feat_drop = nn.Dropout(feat_drop)
        # 用于处理序列特征的线性层
        self.fc_u = nn.Linear(input_dim, hidden_dim, bias=False)
        # 用于处理目标特征的线性层
        self.fc_v = nn.Linear(input_dim, hidden_dim, bias=True)
        # 用于生成注意力分数的线性层（输出2维：保留/丢弃）
        self.fc_e = nn.Linear(hidden_dim, 2, bias=False)
        # 输出投影层（可选）
        self.fc_out = (
            nn.Linear(input_dim, output_dim, bias=False)
            if output_dim != input_dim else None
        )
        # 用于融合长期和短期表示的MLP
        self.mlp_n_ls = nn.Linear(input_dim + hidden_dim, hidden_dim)
        # 激活函数
        self.activation = activation
        # Sigmoid激活函数
        self.sigmoid = nn.Sigmoid()
        # Softmax激活函数
        self.softmax = nn.Softmax(dim=1)

    def forward(self, feat, last_nodes, mask):
        """
        前向传播

        Args:
            feat: 序列特征，形状为[batch_size, seq_len, hidden_dim]
            last_nodes: 目标节点特征，形状为[batch_size, 1, hidden_dim]
            mask: 掩码，标记有效位置

        Returns:
            score: 注意力分数
            rst: 长期表示
            item_level_seq_representation: 项目级序列表示
        """
        # 应用批归一化（如果存在）
        if self.batch_norm is not None:
            feat = self.batch_norm(feat)

        # 应用掩码
        feat = feat * mask
        # 应用dropout
        feat = self.feat_drop(feat)
        # 再次应用掩码
        feat = feat * mask

        # 处理序列特征
        feat_u = self.fc_u(feat)
        feat_u = feat_u * mask

        # 处理目标特征，形状为[batch_size, hidden_dim]
        feat_v = self.fc_v(last_nodes)

        # 计算注意力分数
        # 使用tanh激活函数融合序列特征和目标特征
        e = self.fc_e(F.tanh(feat_u + feat_v)) * mask
        # 使用sigmoid激活函数
        e = self.sigmoid(e)

        # 获取短期表示（目标节点特征）
        short_term = last_nodes.squeeze()

        # 获取长期表示
        e0, rst = self.get_long_term(e, feat, mask)

        # 融合长期和短期表示
        fuse_long_short = torch.cat((rst, short_term), dim=-1)
        # 通过MLP生成项目级序列表示
        item_level_seq_representation = self.mlp_n_ls(self.feat_drop(fuse_long_short))

        # 返回注意力分数（去除维度）
        score = e.squeeze()
        return score, rst, item_level_seq_representation

    def get_long_term(self, e, feat, mask):
        """
        计算长期表示

        Args:
            e: 注意力分数，形状为[batch_size, seq_len, 2]
            feat: 序列特征
            mask: 掩码

        Returns:
            e0: 归一化后的注意力权重
            rst: 长期表示
        """
        # e是2维的分数，rst是long-term representation
        # 创建掩码，将无效位置设为很大的负数
        mask1 = (mask - 1) * 2e32
        # 提取"保留"的分数（第二维），并加上掩码
        e0 = e[:, :, 1] + mask1.squeeze()
        # 使用softmax计算注意力权重
        beta = self.softmax(e0)
        # 根据注意力权重加权特征
        feat_norm = feat * beta.unsqueeze(-1)
        # 应用掩码
        feat_norm = feat_norm * mask
        # 对序列维度求和，得到长期表示
        rst = torch.sum(feat_norm, dim=1)

        # 应用输出投影（如果存在）
        if self.fc_out is not None:
            rst = self.fc_out(rst)
        # 应用激活函数（如果存在）
        if self.activation is not None:
            rst = self.activation(rst)

        return e0, rst
